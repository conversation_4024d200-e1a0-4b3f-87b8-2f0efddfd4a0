'use client';

import React from 'react';
import { format } from 'date-fns';
import { ExternalLink, TrendingUp, TrendingDown, Calendar, Target, Heart, FileText } from 'lucide-react';
import { CorporateFiling } from '@/lib/types';

interface EnhancedFilingCardProps {
  filing: CorporateFiling;
}

// Tag component for displaying filing categories
function TagBadge({ tag }: { tag: string }) {
  return (
    <span className="tag-badge">
      {tag}
    </span>
  );
}

// Sentiment indicator component
function SentimentIndicator({ sentiment }: { sentiment?: 'positive' | 'negative' | 'neutral' }) {
  if (!sentiment) return null;

  const config = {
    positive: { icon: TrendingUp, class: 'sentiment-positive', label: 'Positive' },
    negative: { icon: TrendingDown, class: 'sentiment-negative', label: 'Negative' },
    neutral: { icon: Heart, class: 'sentiment-neutral', label: 'Neutral' }
  };

  const { icon: Icon, class: className, label } = config[sentiment];

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${className}`}>
      <Icon className="w-3 h-3" />
      <span>{label}</span>
    </div>
  );
}

// Impact indicator component
function ImpactIndicator({ impact, reasoning }: { impact?: 'high' | 'medium' | 'low'; reasoning?: string }) {
  if (!impact) return null;

  const config = {
    high: { class: 'impact-high', label: 'High Impact' },
    medium: { class: 'impact-medium', label: 'Medium Impact' },
    low: { class: 'impact-low', label: 'Low Impact' }
  };

  const { class: className, label } = config[impact];

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${className}`} title={reasoning}>
      <Target className="w-3 h-3" />
      <span>{label}</span>
    </div>
  );
}

// Key events component
interface KeyEvents {
  primary_event?: string;
  event_date?: string;
  other_important_dates?: string[];
}

function KeyEventsDisplay({ keyEvents }: { keyEvents?: KeyEvents }) {
  if (!keyEvents?.primary_event) return null;

  // Helper function to safely format dates
  const formatSafeDate = (dateString: string, formatStr: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Return original string if invalid date
      }
      return format(date, formatStr);
    } catch {
      return dateString; // Return original string if formatting fails
    }
  };

  return (
    <div className="mt-3 p-3 bg-slate-50 rounded-lg border border-slate-200">
      <div className="flex items-start gap-2">
        <Calendar className="w-4 h-4 text-slate-500 mt-0.5 flex-shrink-0" />
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium text-slate-900">{keyEvents.primary_event}</p>
          {keyEvents.event_date && (
            <p className="text-xs text-slate-600 mt-1">
              Date: {formatSafeDate(keyEvents.event_date, 'MMM dd, yyyy')}
            </p>
          )}
          {keyEvents.other_important_dates && keyEvents.other_important_dates.length > 0 && (
            <div className="mt-2">
              <p className="text-xs text-slate-500 mb-1">Other important dates:</p>
              <div className="flex flex-wrap gap-1">
                {keyEvents.other_important_dates.map((date: string, index: number) => (
                  <span key={index} className="text-xs bg-slate-100 text-slate-600 px-2 py-0.5 rounded">
                    {formatSafeDate(date, 'MMM dd')}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Enhanced symbols display
function SymbolsDisplay({ filing }: { filing: CorporateFiling }) {
  return (
    <div className="flex flex-wrap gap-2 mt-2">
      {filing.nse_symbol && (
        <span className="text-xs text-emerald-700 bg-emerald-50 border border-emerald-200 px-2 py-0.5 rounded font-medium">
          NSE: {filing.nse_symbol}
        </span>
      )}
      {filing.bse_code && (
        <span className="text-xs text-blue-700 bg-blue-50 border border-blue-200 px-2 py-0.5 rounded font-medium">
          BSE: {filing.bse_code}
        </span>
      )}
      {filing.source_exchange && (
        <span className="text-xs text-slate-600 bg-slate-100 border border-slate-200 px-2 py-0.5 rounded">
          {filing.source_exchange}
        </span>
      )}
    </div>
  );
}

export default function EnhancedFilingCard({ filing }: EnhancedFilingCardProps) {
  const summary = filing.summary;

  // Helper function to safely format dates
  const formatSafeDate = (dateString: string | undefined, formatStr: string) => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return null;
      }
      return format(date, formatStr);
    } catch {
      return null;
    }
  };

  // Use broadcast_date_time if available, fallback to created_at
  const displayDate = filing.broadcast_date_time || filing.created_at;
  const formattedDate = displayDate
    ? formatSafeDate(displayDate, 'MMMM dd, yyyy') || 'Date not available'
    : 'Date not available';

  const formattedBroadcastTime = filing.broadcast_date_time
    ? formatSafeDate(filing.broadcast_date_time, 'MMM dd, yyyy HH:mm')
    : null;

  // Calculate time ago
  const timeAgo = displayDate
    ? (() => {
        try {
          const now = new Date();
          const date = new Date(displayDate);
          if (isNaN(date.getTime())) return null;

          const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

          if (diffInHours < 1) return 'Just now';
          if (diffInHours < 24) return `${diffInHours}h ago`;
          const diffInDays = Math.floor(diffInHours / 24);
          if (diffInDays < 7) return `${diffInDays}d ago`;
          const diffInWeeks = Math.floor(diffInDays / 7);
          return `${diffInWeeks}w ago`;
        } catch {
          return null;
        }
      })()
    : null;

  // Handle PDF viewing
  const handleViewPost = () => {
    if (filing.attachmentfiles) {
      window.open(filing.attachmentfiles, '_blank');
    }
  };

  // Get subcategory badge styling
  const getBadgeInfo = (subcategory: string) => {
    const highPriorityCategories = ['Results', 'Merger', 'Acquisition', 'IPO'];
    const isHighPriority = highPriorityCategories.some(cat => 
      subcategory.toLowerCase().includes(cat.toLowerCase())
    );
    
    return {
      priority: isHighPriority ? 'high' : 'normal',
      color: isHighPriority 
        ? 'bg-red-50 text-red-700 border-red-200' 
        : 'bg-slate-50 text-slate-700 border-slate-200'
    };
  };

  const badgeInfo = filing.subcategory ? getBadgeInfo(filing.subcategory) : { priority: 'normal', color: 'bg-slate-50 text-slate-700 border-slate-200' };

  return (
    <article className="bg-white border border-slate-200 rounded-xl shadow-sm p-4 sm:p-6 hover:shadow-md transition-shadow">
      {/* Header with Broadcast Date/Time at Top Right */}
      <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="heading-sans text-base sm:text-lg text-[#2c374b] transition-colors hover:text-[#3b4a61] hover:underline cursor-pointer break-words">
            {filing.company_name || 'Company Name Not Available'}
          </h3>
          <div className="flex flex-col gap-1 mt-1">
            <div className="flex items-center gap-2">
              <p className="text-xs text-slate-500 body-sans">{formattedDate}</p>
              {timeAgo && (
                <>
                  <span className="text-xs text-slate-300">•</span>
                  <p className="text-xs text-slate-500 body-sans">{timeAgo}</p>
                </>
              )}
            </div>
          </div>

          {/* Enhanced Symbols Display */}
          <SymbolsDisplay filing={filing} />
        </div>

        {/* Broadcast Date/Time at Top Right */}
        <div className="flex flex-col items-end gap-1 flex-shrink-0">
          {formattedBroadcastTime && (
            <>
              <div className="flex items-center gap-1 text-xs text-slate-500 body-sans">
                <Calendar className="w-3 h-3" />
                <span>{formatSafeDate(filing.broadcast_date_time, 'MMM dd, yyyy')}</span>
              </div>
              <div className="text-xs text-slate-400 body-sans">
                {formatSafeDate(filing.broadcast_date_time, 'HH:mm')}
              </div>
            </>
          )}
          {filing.subcategory && (
            <div className="flex items-center gap-1 mt-1">
              {badgeInfo.priority === 'high' && (
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              )}
              <span className={`text-xs font-semibold px-2 py-1 rounded-full border ${badgeInfo.color} whitespace-nowrap body-sans-medium`}>
                {filing.subcategory}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="mt-4 pt-4 border-t border-slate-100">
        {/* Enhanced Headline */}
        {(summary?.headline || filing.headline) && (
          <h2 className="heading-sans-bold text-base sm:text-lg text-slate-900 mb-3 break-words">
            {summary?.headline || filing.headline}
          </h2>
        )}



        {/* Summary */}
        {summary?.summary && (
          <p className="mt-3 text-sm text-slate-600 leading-relaxed break-words body-sans">
            {summary.summary}
          </p>
        )}

        {/* Key Events */}
        {summary?.key_events && (
          <KeyEventsDisplay keyEvents={summary.key_events} />
        )}

        {/* Confidence Score */}
        {summary?.confidence_score && (
          <div className="mt-4 flex items-center gap-1 text-xs text-slate-500 body-sans">
            <span>Confidence:</span>
            <div className="flex items-center gap-1">
              <div className="w-12 h-1.5 bg-slate-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all ${
                    summary.confidence_score >= 0.8 ? 'bg-green-500' :
                    summary.confidence_score >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${summary.confidence_score * 100}%` }}
                />
              </div>
              <span className="font-medium">
                {Math.round(summary.confidence_score * 100)}%
              </span>
            </div>
          </div>
        )}

        {/* Bottom Section: Tags on Left, View Post Button on Right */}
        <div className="mt-4 pt-3 border-t border-slate-100">
          <div className="flex items-center justify-between gap-3">
            {/* Tags on Bottom Left */}
            <div className="flex flex-wrap gap-2 flex-1">
              {summary && (summary.tags || summary.sentiment || summary.impact) && (
                <>
                  {summary.tags?.map((tag, index) => (
                    <TagBadge key={index} tag={tag} />
                  ))}
                  <SentimentIndicator sentiment={summary.sentiment} />
                  <ImpactIndicator impact={summary.impact} reasoning={summary.impact_reasoning} />
                </>
              )}
            </div>

            {/* View Post Button on Bottom Right */}
            <button
              onClick={handleViewPost}
              disabled={!filing.attachmentfiles}
              className={`inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors body-sans-medium flex-shrink-0 ${
                filing.attachmentfiles
                  ? 'bg-[#2c374b] text-white hover:bg-[#3b4a61] shadow-sm'
                  : 'bg-slate-100 text-slate-400 cursor-not-allowed'
              }`}
            >
              <FileText className="w-4 h-4" />
              <span>View Post</span>
              {filing.attachmentfiles && <ExternalLink className="w-3 h-3" />}
            </button>
          </div>
        </div>
      </div>
    </article>
  );
}
