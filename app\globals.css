@import "tailwindcss";

:root {
  --strikedeck-blue: #2c374b;
  --strikedeck-blue-light: #f1f5f9;
  --strikedeck-blue-dark: #3b4a61;
}

@theme inline {
  --font-inter: var(--font-inter);
  --color-strikedeck-blue: var(--strikedeck-blue);
  --color-strikedeck-blue-light: var(--strikedeck-blue-light);
  --color-strikedeck-blue-dark: var(--strikedeck-blue-dark);

  /* Font family utilities - using sans-serif for everything */
  --font-family-sans: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
  --font-family-serif: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
}

/* Custom scrollbar for a more refined look */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Style for the active navigation link */
.nav-link.active {
  background-color: #f1f5f9;
  color: #2c374b;
  font-weight: 600;
}

.main-content {
  height: 100vh;
  overflow-y: auto;
}

/* Professional Typography Classes - All Sans-Serif */
.heading-sans {
  font-family: var(--font-inter);
  font-weight: 600;
  letter-spacing: -0.025em;
}

.heading-sans-bold {
  font-family: var(--font-inter);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.body-sans {
  font-family: var(--font-inter);
  font-weight: 400;
  line-height: 1.6;
}

.body-sans-medium {
  font-family: var(--font-inter);
  font-weight: 500;
  line-height: 1.6;
}

/* Legacy class names for backward compatibility */
.heading-serif {
  font-family: var(--font-inter);
  font-weight: 600;
  letter-spacing: -0.025em;
}

.heading-serif-bold {
  font-family: var(--font-inter);
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Enhanced component styles */
.sentiment-positive {
  @apply bg-green-50 text-green-700 border-green-200;
}

.sentiment-negative {
  @apply bg-red-50 text-red-700 border-red-200;
}

.sentiment-neutral {
  @apply bg-slate-50 text-slate-700 border-slate-200;
}

.impact-high {
  @apply bg-orange-50 text-orange-700 border-orange-200;
}

.impact-medium {
  @apply bg-blue-50 text-blue-700 border-blue-200;
}

.impact-low {
  @apply bg-gray-50 text-gray-700 border-gray-200;
}

.tag-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-50 text-indigo-700 border border-indigo-200;
}
