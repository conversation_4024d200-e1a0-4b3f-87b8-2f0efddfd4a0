@import "tailwindcss";

:root {
  --strikedeck-blue: #2c374b;
  --strikedeck-blue-light: #f1f5f9;
  --strikedeck-blue-dark: #3b4a61;
}

@theme inline {
  --font-poppins: var(--font-poppins);
  --color-strikedeck-blue: var(--strikedeck-blue);
  --color-strikedeck-blue-light: var(--strikedeck-blue-light);
  --color-strikedeck-blue-dark: var(--strikedeck-blue-dark);
}

/* Custom scrollbar for a more refined look */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Style for the active navigation link */
.nav-link.active {
  background-color: #f1f5f9;
  color: #2c374b;
  font-weight: 600;
}

.main-content {
  height: 100vh;
  overflow-y: auto;
}
