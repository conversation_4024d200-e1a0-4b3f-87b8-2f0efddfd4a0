2025-07-21 14:13:57,174 - ai_document_analyzer - INFO - Loaded document types from D:\pkeday\corporate_filings\enhanced_document_analyzer\document_types.yaml
2025-07-21 14:13:57,572 - ai_document_analyzer - INFO - Supabase client initialized successfully
2025-07-21 14:13:57,858 - ai_document_analyzer - INFO - OpenAI client initialized successfully
2025-07-21 14:13:58,936 - httpx - INFO - HTTP Request: GET https://yvuwseolouiqhoxsieop.supabase.co/rest/v1/master_corporate_announcements?select=id&limit=1 "HTTP/2 200 OK"
2025-07-21 14:13:59,215 - httpx - INFO - HTTP Request: GET https://yvuwseolouiqhoxsieop.supabase.co/rest/v1/master_corporate_announcements?select=%2A&id=eq.1dc88cf7-7088-4cf4-ba89-d812a3477c7a "HTTP/2 200 OK"
2025-07-21 14:13:59,225 - ai_document_analyzer - INFO - Processing record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a for JSON summary
2025-07-21 14:13:59,225 - ai_document_analyzer - INFO - Classified record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a as general_update with confidence 0.3
2025-07-21 14:14:04,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 14:14:04,384 - ai_document_analyzer - INFO - Successfully saved analysis for record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a to test_summary_1dc88cf7-7088-4cf4-ba89-d812a3477c7a.json
2025-07-21 14:14:22,151 - __main__ - INFO - Loaded document types from D:\pkeday\corporate_filings\enhanced_document_analyzer\document_types.yaml
2025-07-21 14:14:22,665 - __main__ - INFO - Supabase client initialized successfully
2025-07-21 14:14:23,071 - __main__ - INFO - OpenAI client initialized successfully
2025-07-21 14:14:23,800 - httpx - INFO - HTTP Request: GET https://yvuwseolouiqhoxsieop.supabase.co/rest/v1/master_corporate_announcements?select=%2A&id=eq.1dc88cf7-7088-4cf4-ba89-d812a3477c7a "HTTP/2 200 OK"
2025-07-21 14:14:23,807 - __main__ - INFO - Processing record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a for JSON summary
2025-07-21 14:14:23,807 - __main__ - INFO - Classified record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a as general_update with confidence 0.3
2025-07-21 14:14:27,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 14:14:27,946 - __main__ - INFO - Successfully saved analysis for record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a to example_summary.json
2025-07-21 14:14:51,481 - ai_document_analyzer - INFO - Loaded document types from D:\pkeday\corporate_filings\enhanced_document_analyzer\document_types.yaml
2025-07-21 14:14:51,796 - ai_document_analyzer - INFO - Supabase client initialized successfully
2025-07-21 14:14:52,071 - ai_document_analyzer - INFO - OpenAI client initialized successfully
2025-07-21 14:14:52,607 - httpx - INFO - HTTP Request: GET https://yvuwseolouiqhoxsieop.supabase.co/rest/v1/master_corporate_announcements?select=%2A&id=eq.1dc88cf7-7088-4cf4-ba89-d812a3477c7a "HTTP/2 200 OK"
2025-07-21 14:14:52,610 - ai_document_analyzer - INFO - Processing record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a for JSON summary
2025-07-21 14:14:52,610 - ai_document_analyzer - INFO - Classified record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a as general_update with confidence 0.3
2025-07-21 14:14:57,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-21 14:14:57,244 - ai_document_analyzer - INFO - Successfully saved analysis for record 1dc88cf7-7088-4cf4-ba89-d812a3477c7a to test_dedicated_script.json
