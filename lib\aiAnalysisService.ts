import { CorporateFiling, FilingSummary } from './types';

export interface AIAnalysisResult {
  impact: 'high' | 'medium' | 'low';
  sentiment: 'positive' | 'negative' | 'neutral';
  impact_reasoning: string;
  key_insights: string[];
  market_implications: string;
  confidence_score: number;
}

export class AIAnalysisService {
  private static instance: AIAnalysisService;

  public static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * Generate AI analysis for a corporate filing
   */
  async generateAnalysis(filing: CorporateFiling): Promise<AIAnalysisResult> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

    // If we already have AI analysis from the backend, use it
    if (filing.summary) {
      return this.enhanceExistingAnalysis(filing.summary);
    }

    // Generate new analysis based on filing content
    return this.analyzeFilingContent(filing);
  }

  /**
   * Enhance existing AI analysis with additional insights
   */
  private enhanceExistingAnalysis(summary: FilingSummary): AIAnalysisResult {
    const keyInsights = this.extractKeyInsights(summary);
    const marketImplications = this.generateMarketImplications(summary);

    return {
      impact: summary.impact || 'medium',
      sentiment: summary.sentiment || 'neutral',
      impact_reasoning: summary.impact_reasoning || 'Analysis based on filing content and market context.',
      key_insights: keyInsights,
      market_implications: marketImplications,
      confidence_score: summary.confidence_score || 0.75
    };
  }

  /**
   * Analyze filing content to generate new insights
   */
  private analyzeFilingContent(filing: CorporateFiling): AIAnalysisResult {
    const content = `${filing.headline || ''} ${filing.details || ''}`.toLowerCase();
    const subcategory = filing.subcategory?.toLowerCase() || '';

    // Determine impact based on content analysis
    const impact = this.determineImpact(content, subcategory);
    
    // Determine sentiment based on keywords
    const sentiment = this.determineSentiment(content);
    
    // Generate reasoning
    const impact_reasoning = this.generateImpactReasoning(impact, sentiment, subcategory);
    
    // Extract key insights
    const key_insights = this.generateKeyInsights(content, subcategory);
    
    // Generate market implications
    const market_implications = this.generateMarketImplications({ 
      summary: content, 
      impact, 
      sentiment 
    } as FilingSummary);

    return {
      impact,
      sentiment,
      impact_reasoning,
      key_insights,
      market_implications,
      confidence_score: 0.80
    };
  }

  /**
   * Determine impact level based on content analysis
   */
  private determineImpact(content: string, subcategory: string): 'high' | 'medium' | 'low' {
    const highImpactKeywords = [
      'merger', 'acquisition', 'ipo', 'results', 'earnings', 'profit', 'loss',
      'dividend', 'buyback', 'rights issue', 'fundraising', 'investment'
    ];
    
    const highImpactCategories = [
      'results', 'merger', 'acquisition', 'ipo', 'fundraising'
    ];

    const mediumImpactKeywords = [
      'board meeting', 'appointment', 'resignation', 'partnership', 'contract',
      'expansion', 'launch', 'agreement'
    ];

    // Check category first
    if (highImpactCategories.some(cat => subcategory.includes(cat))) {
      return 'high';
    }

    // Check content keywords
    const highImpactCount = highImpactKeywords.filter(keyword => 
      content.includes(keyword)
    ).length;

    const mediumImpactCount = mediumImpactKeywords.filter(keyword => 
      content.includes(keyword)
    ).length;

    if (highImpactCount >= 2) return 'high';
    if (highImpactCount >= 1 || mediumImpactCount >= 2) return 'medium';
    return 'low';
  }

  /**
   * Determine sentiment based on content analysis
   */
  private determineSentiment(content: string): 'positive' | 'negative' | 'neutral' {
    const positiveKeywords = [
      'growth', 'profit', 'increase', 'expansion', 'success', 'achievement',
      'improvement', 'strong', 'robust', 'excellent', 'outstanding', 'record',
      'dividend', 'bonus', 'award', 'win', 'launch', 'partnership'
    ];

    const negativeKeywords = [
      'loss', 'decline', 'decrease', 'fall', 'drop', 'weak', 'poor',
      'challenge', 'difficulty', 'issue', 'problem', 'concern', 'risk',
      'litigation', 'dispute', 'penalty', 'fine', 'investigation'
    ];

    const positiveCount = positiveKeywords.filter(keyword => 
      content.includes(keyword)
    ).length;

    const negativeCount = negativeKeywords.filter(keyword => 
      content.includes(keyword)
    ).length;

    if (positiveCount > negativeCount && positiveCount >= 2) return 'positive';
    if (negativeCount > positiveCount && negativeCount >= 2) return 'negative';
    return 'neutral';
  }

  /**
   * Generate impact reasoning
   */
  private generateImpactReasoning(
    impact: string, 
    sentiment: string, 
    subcategory: string
  ): string {
    const reasoningMap = {
      high: {
        positive: 'This announcement is likely to have significant positive impact on stock price and investor sentiment due to its strategic importance.',
        negative: 'This development may have substantial negative implications for the company\'s financial performance and market position.',
        neutral: 'While this is a significant corporate development, its immediate market impact may be limited pending further details.'
      },
      medium: {
        positive: 'This positive development should moderately benefit the company\'s market perception and operational efficiency.',
        negative: 'This issue may create moderate headwinds for the company but is likely manageable with proper execution.',
        neutral: 'This routine corporate action is part of normal business operations with moderate significance for stakeholders.'
      },
      low: {
        positive: 'This is a minor positive development that supports the company\'s ongoing operations.',
        negative: 'This represents a minor challenge that is unlikely to significantly impact the company\'s overall performance.',
        neutral: 'This is a routine filing with minimal immediate impact on business operations or market perception.'
      }
    };

    return reasoningMap[impact as keyof typeof reasoningMap][sentiment as keyof typeof reasoningMap.high];
  }

  /**
   * Generate key insights from content
   */
  private generateKeyInsights(content: string, subcategory: string): string[] {
    const insights: string[] = [];

    // Category-specific insights
    if (subcategory.includes('result')) {
      insights.push('Financial performance update with potential impact on quarterly guidance');
    }
    if (subcategory.includes('board')) {
      insights.push('Corporate governance development affecting leadership structure');
    }
    if (subcategory.includes('dividend') || content.includes('dividend')) {
      insights.push('Shareholder return initiative indicating strong cash position');
    }
    if (content.includes('expansion') || content.includes('investment')) {
      insights.push('Growth initiative suggesting confidence in market opportunities');
    }

    // Default insights if none found
    if (insights.length === 0) {
      insights.push('Corporate development requiring stakeholder attention');
      insights.push('Regulatory compliance filing with business implications');
    }

    return insights.slice(0, 3); // Limit to 3 insights
  }

  /**
   * Extract key insights from existing summary
   */
  private extractKeyInsights(summary: FilingSummary): string[] {
    const insights: string[] = [];
    
    if (summary.tags) {
      summary.tags.forEach(tag => {
        switch (tag.toLowerCase()) {
          case 'results':
            insights.push('Financial performance disclosure with earnings implications');
            break;
          case 'corporate actions':
            insights.push('Shareholder value initiative affecting stock structure');
            break;
          case 'fundraising':
            insights.push('Capital raising activity for growth or debt management');
            break;
          case 'mergers & acquisitions':
            insights.push('Strategic transaction with significant business impact');
            break;
          default:
            insights.push(`${tag} related development with operational significance`);
        }
      });
    }

    return insights.slice(0, 3);
  }

  /**
   * Generate market implications
   */
  private generateMarketImplications(summary: any): string {
    const impact = summary.impact || 'medium';
    const sentiment = summary.sentiment || 'neutral';

    if (impact === 'high' && sentiment === 'positive') {
      return 'Strong positive catalyst likely to drive investor interest and potential stock price appreciation.';
    }
    if (impact === 'high' && sentiment === 'negative') {
      return 'Significant concern that may lead to selling pressure and increased volatility.';
    }
    if (impact === 'medium' && sentiment === 'positive') {
      return 'Moderately positive development supporting medium-term investment thesis.';
    }
    if (impact === 'medium' && sentiment === 'negative') {
      return 'Moderate headwind that investors should monitor for potential impact on fundamentals.';
    }
    
    return 'Routine corporate development with limited immediate market implications.';
  }
}

export const aiAnalysisService = AIAnalysisService.getInstance();
