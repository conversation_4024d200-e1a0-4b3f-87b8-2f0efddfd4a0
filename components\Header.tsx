'use client';

import { useState } from 'react';
import { Search, User, ChevronDown, Menu } from 'lucide-react';

interface HeaderProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onMenuClick?: () => void;
}

export default function Header({ searchValue, onSearchChange, onMenuClick }: HeaderProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  return (
    <header className="sticky top-0 bg-slate-50/80 backdrop-blur-sm z-10 flex justify-between items-center p-4 sm:p-6 lg:p-8 border-b border-slate-200">
      {/* Mobile Menu Button + Search Bar */}
      <div className="flex items-center gap-4 flex-1">
        {/* Mobile Menu Button */}
        <button
          onClick={onMenuClick}
          className="p-2 text-slate-600 hover:text-slate-800 transition-colors lg:hidden"
        >
          <Menu className="w-5 h-5" />
        </button>

        {/* Search Bar */}
        <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="text-slate-400 w-5 h-5" />
        </div>
          <input
            type="text"
            placeholder="Search companies, filings..."
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full bg-white border border-slate-300 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-slate-400 transition text-sm sm:text-base"
          />
        </div>
      </div>

      {/* User Profile Dropdown */}
      <div className="relative">
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="flex items-center gap-3 text-slate-600 hover:text-slate-800 transition-colors"
        >
          <span className="font-semibold text-sm hidden sm:inline">John Doe</span>
          <User className="text-3xl w-8 h-8 p-1 rounded-full bg-slate-200" />
          <ChevronDown 
            className={`text-sm w-4 h-4 transition-transform ${
              isDropdownOpen ? 'rotate-180' : ''
            }`} 
          />
        </button>

        {/* Dropdown Menu */}
        {isDropdownOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsDropdownOpen(false)}
            />
            
            {/* Dropdown Content */}
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
              <a 
                href="#" 
                className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                onClick={() => setIsDropdownOpen(false)}
              >
                My Account
              </a>
              <a 
                href="#" 
                className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                onClick={() => setIsDropdownOpen(false)}
              >
                Settings
              </a>
              <hr className="border-slate-200 my-1" />
              <a 
                href="#" 
                className="block px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                onClick={() => setIsDropdownOpen(false)}
              >
                Logout
              </a>
            </div>
          </>
        )}
      </div>
    </header>
  );
}
