'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchCorporateFilings } from '@/lib/supabase';
import { CorporateFiling } from '@/lib/types';
import EnhancedFilingCard from './EnhancedFilingCard';
import FilingCardSkeleton from './FilingCardSkeleton';
import FilterPanel from './FilterPanel';
import SortDropdown from './SortDropdown';
import { useAppContext } from './MainLayout';
import { AlertCircle, RefreshCw } from 'lucide-react';

export default function CorporateFilingsFeed() {
  const { searchValue, filters, setFilters, sort, setSort } = useAppContext();
  const [filings, setFilings] = useState<CorporateFiling[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const pageSize = 20; // Increased page size for infinite scroll

  const loadFilings = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) {
        setLoading(true);
      }
      setError(null);

      // Combine search value with filters
      const combinedFilters = {
        ...filters,
        search: searchValue || filters.search,
      };

      const response = await fetchCorporateFilings(page, pageSize, combinedFilters, sort);

      if (response.error) {
        setError(response.error);
        return;
      }

      if (append) {
        setFilings(prev => [...prev, ...response.data]);
      } else {
        setFilings(response.data);
      }

      setTotalCount(response.count);
      setHasMore(response.data.length === pageSize);
      setCurrentPage(page);
    } catch (err) {
      setError('Failed to load corporate filings');
      console.error('Error loading filings:', err);
    } finally {
      setLoading(false);
    }
  }, [searchValue, filters, sort, pageSize]);

  // Load more function for infinite scroll
  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await loadFilings(currentPage + 1, true);
    }
  }, [loading, hasMore, currentPage, loadFilings]);

  // Simple infinite scroll with intersection observer
  const [isFetching, setIsFetching] = useState(false);
  const lastElementRef = useCallback((node: HTMLElement | null) => {
    if (loading || isFetching) return;
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasMore && !loading && !isFetching) {
        setIsFetching(true);
        loadMore().finally(() => setIsFetching(false));
      }
    }, { threshold: 0.8, rootMargin: '200px' });

    if (node) observer.current.observe(node);
  }, [loading, isFetching, hasMore, loadMore]);

  const observer = useRef<IntersectionObserver | null>(null);

  const refresh = () => {
    setCurrentPage(1);
    setHasMore(true);
    loadFilings(1, false);
  };

  useEffect(() => {
    loadFilings(1, false);
  }, [loadFilings]);

  // Reload when search, filters, or sort change
  useEffect(() => {
    setCurrentPage(1);
    setHasMore(true);
    loadFilings(1, false);
  }, [searchValue, filters, sort, loadFilings]);

  if (loading && filings.length === 0) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        <div className="lg:col-span-2 space-y-6">
          {Array.from({ length: 5 }).map((_, index) => (
            <FilingCardSkeleton key={index} />
          ))}
        </div>
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl p-6 border border-slate-200">
            <div className="animate-pulse">
              <div className="h-6 bg-slate-200 rounded w-3/4 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-slate-200 rounded"></div>
                <div className="h-4 bg-slate-200 rounded w-5/6"></div>
                <div className="h-4 bg-slate-200 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <h3 className="heading-sans text-lg text-slate-900 mb-2">Error Loading Filings</h3>
        <p className="body-sans text-slate-600 mb-4 text-center max-w-md">{error}</p>
        <button
          onClick={refresh}
          className="flex items-center gap-2 px-4 py-2 bg-[#2c374b] text-white rounded-lg hover:bg-[#3b4a61] transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
      {/* Main Feed Column */}
      <div className="lg:col-span-2 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="heading-sans-bold text-xl sm:text-2xl text-slate-900">Corporate Filings</h1>
            <p className="body-sans text-slate-600 mt-1 text-sm sm:text-base">
              {totalCount.toLocaleString()} filings found
              {(searchValue || filters.company || filters.subcategory) && ' (filtered)'}
            </p>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <SortDropdown sort={sort} onSortChange={setSort} />
            <button
              onClick={refresh}
              disabled={loading}
              className="flex items-center gap-2 px-3 sm:px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors disabled:opacity-50 text-sm"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        <div className="mb-6">
          <FilterPanel
            filters={filters}
            onFiltersChange={setFilters}
            isOpen={isFilterOpen}
            onToggle={() => setIsFilterOpen(!isFilterOpen)}
          />
        </div>

        {/* Filings List */}
        {filings.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-slate-600">No corporate filings found.</p>
          </div>
        ) : (
          <>
            <div className="space-y-6">
              {filings.map((filing, index) => {
                const isLast = index === filings.length - 1;
                return (
                  <div
                    key={filing.id}
                    ref={isLast ? lastElementRef : null}
                    className="w-full"
                  >
                    <EnhancedFilingCard filing={filing} />
                  </div>
                );
              })}
            </div>

            {/* Loading indicator for infinite scroll */}
            {(isFetching || loading) && hasMore && (
              <div className="mt-6 space-y-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <FilingCardSkeleton key={`loading-${index}`} />
                ))}
              </div>
            )}

            {/* End of results indicator */}
            {!hasMore && filings.length > 0 && (
              <div className="text-center py-8 border-t border-slate-200 mt-8">
                <p className="text-slate-500 text-sm">
                  You&apos;ve reached the end of the results
                </p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Right Sidebar */}
      <div className="lg:col-span-1 space-y-6 lg:space-y-8">
        {/* Quick Stats */}
        <div className="bg-white rounded-xl p-6 border border-slate-200">
          <h2 className="heading-sans-bold text-lg text-[#2c374b] mb-4">Quick Stats</h2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="body-sans text-slate-600">Total Filings</span>
              <span className="body-sans-medium">{totalCount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="body-sans text-slate-600">This Page</span>
              <span className="body-sans-medium">{filings.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="body-sans text-slate-600">Current Page</span>
              <span className="body-sans-medium">{currentPage}</span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl p-6 border border-slate-200">
          <h2 className="heading-sans-bold text-lg text-[#2c374b] mb-4">Recent Activity</h2>
          <div className="space-y-3">
            <div className="text-sm text-slate-600">
              <p className="body-sans-medium">Latest Broadcast</p>
              <p className="body-sans text-xs text-slate-500">
                {filings[0]?.broadcast_date_time
                  ? new Date(filings[0].broadcast_date_time).toLocaleDateString()
                  : filings[0]?.created_at
                    ? new Date(filings[0].created_at).toLocaleDateString()
                    : 'N/A'
                }
              </p>
            </div>
            <div className="text-sm text-slate-600">
              <p className="body-sans-medium">Most Active Company</p>
              <p className="body-sans text-xs text-slate-500">
                {filings[0]?.company_name || 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
