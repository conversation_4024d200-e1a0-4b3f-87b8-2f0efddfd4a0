# 📄 JSON Summary Export Feature

## Overview

The Enhanced Document Analyzer now includes a powerful feature to summarize individual corporate filing records and export the complete analysis to JSON files. This feature is perfect for:

- **Testing and Analysis**: Examine how the AI processes specific documents
- **Data Export**: Extract structured analysis data for external tools
- **Quality Assurance**: Review AI-generated summaries before batch processing
- **Integration**: Use JSON output in other applications or workflows

## Quick Start

### Method 1: Command Line (Main Script)
```bash
# Basic usage - creates summary_{record_id}.json
python ai_document_analyzer.py --summarize YOUR_RECORD_ID

# Custom output path
python ai_document_analyzer.py --summarize YOUR_RECORD_ID --output my_analysis.json
```

### Method 2: Dedicated Script
```bash
# Basic usage
python summarize_record.py YOUR_RECORD_ID

# With custom output and verbose logging
python summarize_record.py YOUR_RECORD_ID --output detailed_analysis.json --verbose
```

### Method 3: Python Code
```python
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
result = analyzer.summarize_record_to_json('your_record_id', 'output.json')

if "error" not in result:
    print(f"Success! Analysis saved for {result['record_id']}")
else:
    print(f"Error: {result['error']}")
```

## JSON Output Structure

The generated JSON file contains four main sections:

### 1. Record ID
```json
{
  "record_id": "1dc88cf7-7088-4cf4-ba89-d812a3477c7a"
}
```

### 2. Original Data
All the raw data from the database:
```json
{
  "original_data": {
    "headline": "Original headline from database",
    "details": "Original content/details",
    "company_name": "Company Name",
    "broadcast_date_time": "2025-07-21T13:42:28+00:00",
    "created_at": "2025-07-21T08:14:18.349636+00:00",
    "updated_at": "2025-07-21T08:14:18.349636+00:00",
    "url": "source_url",
    "exchange": "BSE/NSE",
    "symbol": "SYMBOL"
  }
}
```

### 3. Classification Results
How the document was classified:
```json
{
  "classification": {
    "document_type": "earnings_call",
    "confidence_score": 0.85,
    "matched_keywords": ["earnings", "call", "results"]
  }
}
```

### 4. AI Analysis
The complete AI-generated analysis:
```json
{
  "ai_analysis": {
    "headline": "AI-generated concise headline (max 80 chars)",
    "type": "earnings_call",
    "summary": "Comprehensive AI-generated summary of the document",
    "key_disclosures": {
      "event": "Main event or announcement",
      "key_points": "Important details and implications"
    },
    "confidence_score": 0.85,
    "filing_date": "2024-01-01",
    "filing_type": "Type of filing",
    "regulatory_body": "BSE/NSE/SEBI",
    "compliance_requirement": "Relevant regulation",
    "processed_at": "2025-07-21T14:14:23.808363"
  }
}
```

### 5. Processing Metadata
Information about the processing:
```json
{
  "processing_metadata": {
    "processed_at": "2025-07-21T14:14:27.944905",
    "analyzer_version": "enhanced_v1.0",
    "model_used": "gpt-4o-mini"
  }
}
```

## Use Cases

### 1. Quality Testing
Test how the AI processes specific documents before running batch operations:
```bash
python summarize_record.py PROBLEMATIC_RECORD_ID --verbose
```

### 2. Data Export for Analysis
Export multiple records for external analysis:
```bash
# Create summaries for multiple records
python summarize_record.py RECORD_1 --output analysis_1.json
python summarize_record.py RECORD_2 --output analysis_2.json
python summarize_record.py RECORD_3 --output analysis_3.json
```

### 3. Integration with Other Tools
Use the JSON output in other applications:
```python
import json

# Load the analysis
with open('summary_record_id.json', 'r') as f:
    analysis = json.load(f)

# Extract specific information
company = analysis['original_data']['company_name']
ai_headline = analysis['ai_analysis']['headline']
confidence = analysis['classification']['confidence_score']

print(f"{company}: {ai_headline} (confidence: {confidence:.3f})")
```

### 4. Debugging and Development
Use verbose mode to understand processing details:
```bash
python summarize_record.py YOUR_RECORD_ID --verbose
```

## Testing

Test the feature with the included test script:
```bash
python test_summarize_feature.py
```

This will:
- Find a sample record from the database
- Process it with the summarize feature
- Display the results
- Clean up the test file
- Show usage examples

## Error Handling

The feature includes comprehensive error handling:

- **Record Not Found**: Returns error if record ID doesn't exist
- **Database Connection**: Handles Supabase connection issues
- **AI Processing**: Graceful fallback if AI analysis fails
- **File Writing**: Handles file system errors

Example error response:
```json
{
  "error": "Record with ID invalid_id not found"
}
```

## Tips

1. **Record IDs**: Use the database to find valid record IDs
2. **Output Paths**: Use descriptive filenames for better organization
3. **Verbose Mode**: Enable for debugging and detailed logging
4. **Batch Processing**: For multiple records, consider using the main batch processing features
5. **File Management**: JSON files can be large; manage storage accordingly

## Integration with Existing Workflow

This feature complements the existing batch processing capabilities:

1. **Test First**: Use summarize feature to test problematic records
2. **Batch Process**: Run normal batch processing for bulk operations
3. **Quality Check**: Use summarize feature to verify results
4. **Export**: Generate JSON summaries for important records

The summarize feature is designed to work seamlessly with the existing Enhanced Document Analyzer while providing new capabilities for individual record analysis and data export.
