#!/usr/bin/env python3
"""
Enhanced AI Document Analyzer with Headline Generation

This module provides comprehensive document analysis capabilities including:
- Document type classification
- Summary generation with headlines
- Structured data extraction
- Batch processing with progress tracking
"""

import os
import json
import yaml
import logging
import argparse
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from pathlib import Path

import requests
from supabase import create_client, Client
import openai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'ai_analysis_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_CONFIG = {
    'supabase_url': 'https://yvuwseolouiqhoxsieop.supabase.co',
    'supabase_key': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow',
    'openai_api_key': '********************************************************************************************************************************************************************',
    'openrouter_api_key': os.getenv('OPENROUTER_API_KEY'),
    'model': 'gpt-4o-mini',
    'max_tokens': 2000,
    'temperature': 0.3,
    'batch_size': 50,
    'log_level': 'INFO'
}

@dataclass
class ClassificationResult:
    """Result of document type classification"""
    confidence_score: float
    classification_method: str
    matched_keywords: List[str]
    fallback_used: bool = False

@dataclass
class DocumentTypeDefinition:
    """Document type definition with classification rules"""
    display_name: str
    keywords: List[str]
    weighted_keywords: Dict[str, float]
    template: Dict[str, Any]
    validation_rules: Dict[str, Any]
    confidence_threshold: float
    priority: int
    enabled: bool = True

class DocumentTypeRegistry:
    """Registry for managing document types"""
    
    def __init__(self):
        self.document_types: Dict[str, DocumentTypeDefinition] = {}
        self.load_default_types()
    
    def load_default_types(self):
        """Load document types from configuration file"""
        config_paths = [
            'document_types.yaml',
            'config/document_types.yaml',
            Path(__file__).parent / 'document_types.yaml'
        ]
        
        for path in config_paths:
            if Path(path).exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                        self._load_from_config(config)
                    logger.info(f"Loaded document types from {path}")
                    return
                except Exception as e:
                    logger.warning(f"Failed to load config from {path}: {e}")
        
        logger.warning("No document type configuration found, using minimal defaults")
        self._load_minimal_defaults()
    
    def _load_from_config(self, config: Dict):
        """Load document types from configuration dictionary"""
        doc_types = config.get('document_types', {})
        for type_id, type_config in doc_types.items():
            if type_config.get('enabled', True):
                self.document_types[type_id] = DocumentTypeDefinition(
                    display_name=type_config.get('display_name', type_id),
                    keywords=type_config.get('keywords', []),
                    weighted_keywords=type_config.get('weighted_keywords', {}),
                    template=type_config.get('template', {}),
                    validation_rules=type_config.get('validation_rules', {}),
                    confidence_threshold=type_config.get('confidence_threshold', 0.5),
                    priority=type_config.get('priority', 1),
                    enabled=type_config.get('enabled', True)
                )
    
    def _load_minimal_defaults(self):
        """Load minimal default document types"""
        self.document_types = {
            'general_update': DocumentTypeDefinition(
                display_name='General Update',
                keywords=['update', 'announcement', 'notice'],
                weighted_keywords={'update': 1.0, 'announcement': 1.2},
                template={'type': 'general_update', 'summary': 'Document summary'},
                validation_rules={},
                confidence_threshold=0.3,
                priority=1
            )
        }
    
    def list_document_types(self) -> List[str]:
        """Get list of available document type IDs"""
        return list(self.document_types.keys())

class DocumentAnalyzer:
    """Enhanced document analyzer with flexible document type system"""
    
    def __init__(self, config: Dict = None):
        self.config = {**DEFAULT_CONFIG, **(config or {})}
        self.document_registry = DocumentTypeRegistry()
        
        # Initialize clients
        self._init_supabase()
        self._init_openai()
        
        # Set logging level
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        logger.setLevel(log_level)
    
    def _init_supabase(self):
        """Initialize Supabase client"""
        if not self.config['supabase_key']:
            logger.warning("SUPABASE_KEY not set, database operations will fail")
            self.supabase = None
            return
        
        try:
            self.supabase: Client = create_client(
                self.config['supabase_url'],
                self.config['supabase_key']
            )
            logger.info("Supabase client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            self.supabase = None
    
    def _init_openai(self):
        """Initialize OpenAI client"""
        api_key = self.config.get('openai_api_key') or self.config.get('openrouter_api_key')
        if not api_key:
            logger.warning("No API key found, AI analysis will fail")
            self.openai_client = None
            return
        
        try:
            if self.config.get('openrouter_api_key'):
                # Use OpenRouter
                self.openai_client = openai.OpenAI(
                    base_url="https://openrouter.ai/api/v1",
                    api_key=self.config['openrouter_api_key']
                )
                logger.info("OpenRouter client initialized successfully")
            else:
                # Use OpenAI directly
                self.openai_client = openai.OpenAI(api_key=self.config['openai_api_key'])
                logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.openai_client = None

    def classify_document_type(self, content: str, headline: str, details: str, record_id: str = None) -> Tuple[str, ClassificationResult]:
        """Classify document type based on content and metadata"""
        try:
            # Handle None values
            content = content or ''
            headline = headline or ''
            details = details or ''

            # Simple keyword-based classification for now
            text_to_analyze = f"{headline} {details} {content}".lower()
            
            best_match = None
            best_score = 0
            matched_keywords = []
            
            for type_id, doc_type in self.document_registry.document_types.items():
                score = 0
                type_keywords = []
                
                # Check regular keywords
                for keyword in doc_type.keywords:
                    if keyword.lower() in text_to_analyze:
                        score += 1
                        type_keywords.append(keyword)
                
                # Check weighted keywords
                for keyword, weight in doc_type.weighted_keywords.items():
                    if keyword.lower() in text_to_analyze:
                        score += weight
                        type_keywords.append(keyword)
                
                # Normalize score by number of keywords
                if doc_type.keywords or doc_type.weighted_keywords:
                    total_keywords = len(doc_type.keywords) + len(doc_type.weighted_keywords)
                    normalized_score = score / total_keywords
                    
                    if normalized_score > best_score and normalized_score >= doc_type.confidence_threshold:
                        best_match = type_id
                        best_score = normalized_score
                        matched_keywords = type_keywords
            
            # Fallback to general_update if no match
            if not best_match:
                best_match = 'general_update'
                best_score = 0.3
                matched_keywords = []
            
            result = ClassificationResult(
                confidence_score=min(best_score, 1.0),
                classification_method='keyword_matching',
                matched_keywords=matched_keywords,
                fallback_used=(best_match == 'general_update' and best_score <= 0.3)
            )
            
            return best_match, result
            
        except Exception as e:
            logger.error(f"Error in document classification: {e}")
            return 'unknown', ClassificationResult(
                confidence_score=0.1,
                classification_method='error_fallback',
                matched_keywords=[],
                fallback_used=True
            )

    def generate_enhanced_prompt(self, doc_type: str, content: str, headline: str, details: str) -> str:
        """Generate enhanced prompt with headline generation"""
        
        base_prompt = f"""
You are an expert financial document analyzer. Analyze the following corporate filing and provide a comprehensive summary with a clear, self-explanatory headline.

Document Type: {doc_type}
Original Headline: {headline}
Details: {details}
Content: {content[:3000]}...

Please provide your analysis in the following JSON format:
{{
    "headline": "A clear, concise, self-explanatory headline (max 80 characters) that captures the key announcement",
    "type": "{doc_type}",
    "summary": "A comprehensive summary of the document content",
    "key_disclosures": {{
        "event": "Main event or announcement",
        "key_points": "Important details and implications"
    }},
    "confidence_score": 0.85,
    "filing_date": "YYYY-MM-DD",
    "filing_type": "Type of filing",
    "regulatory_body": "BSE/NSE/SEBI",
    "compliance_requirement": "Relevant regulation or requirement",
    "processed_at": "{datetime.now().isoformat()}"
}}

Guidelines for headline generation:
1. Keep it under 80 characters
2. Make it self-explanatory and newsworthy
3. Include the company's key action or announcement
4. Avoid jargon and technical terms
5. Focus on the most important aspect of the filing

Examples of good headlines:
- "Reliance Industries Reports 15% Revenue Growth in Q3"
- "TCS Announces ₹16,000 Cr Share Buyback Program"
- "HDFC Bank Board Approves Merger with HDFC Ltd"
- "Infosys Declares Interim Dividend of ₹18 per Share"

Ensure the headline is informative and would make sense to someone seeing it in a news feed.
"""
        
        return base_prompt.strip()

    def analyze_document_with_ai(self, content: str, headline: str, details: str, doc_type: str) -> Dict:
        """Analyze document using AI with enhanced headline generation"""
        if not self.openai_client:
            logger.error("OpenAI client not initialized")
            return None
        
        try:
            # Handle None values
            content = content or ''
            headline = headline or ''
            details = details or ''

            prompt = self.generate_enhanced_prompt(doc_type, content, headline, details)
            logger.debug(f"Generated prompt for {doc_type}: {len(prompt)} characters")

            response = self.openai_client.chat.completions.create(
                model=self.config['model'],
                messages=[
                    {"role": "system", "content": "You are an expert financial document analyzer specializing in corporate filings and announcements."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config['max_tokens'],
                temperature=self.config['temperature']
            )

            logger.debug(f"Received response from OpenAI: {response is not None}")

            # Check if response is valid
            if not response:
                logger.error("No response from OpenAI API")
                return None

            if not hasattr(response, 'choices') or not response.choices:
                logger.error("No choices in OpenAI response")
                return None

            if len(response.choices) == 0:
                logger.error("Empty choices array in OpenAI response")
                return None

            choice = response.choices[0]
            if not hasattr(choice, 'message') or not choice.message:
                logger.error("No message in OpenAI response choice")
                return None

            if not hasattr(choice.message, 'content') or not choice.message.content:
                logger.error("No content in OpenAI response message")
                return None

            result_text = choice.message.content.strip()
            logger.debug(f"AI response text length: {len(result_text)}")
            
            # Try to parse JSON response
            try:
                # Extract JSON from response if it's wrapped in markdown
                if '```json' in result_text:
                    start = result_text.find('```json') + 7
                    end = result_text.find('```', start)
                    result_text = result_text[start:end].strip()
                elif '```' in result_text:
                    start = result_text.find('```') + 3
                    end = result_text.find('```', start)
                    result_text = result_text[start:end].strip()
                
                result = json.loads(result_text)
                
                # Ensure headline is present and properly formatted
                if 'headline' not in result or not result['headline']:
                    result['headline'] = self._generate_fallback_headline(headline, doc_type)
                
                # Truncate headline if too long
                if len(result['headline']) > 80:
                    result['headline'] = result['headline'][:77] + "..."
                
                return result
                
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse AI response as JSON: {e}")
                # Return structured fallback
                return {
                    "headline": self._generate_fallback_headline(headline, doc_type),
                    "type": doc_type,
                    "summary": result_text[:500] + "..." if len(result_text) > 500 else result_text,
                    "key_disclosures": {"event": "Document analysis", "key_points": "See summary"},
                    "confidence_score": 0.6,
                    "filing_date": datetime.now().strftime("%Y-%m-%d"),
                    "filing_type": doc_type,
                    "regulatory_body": "Unknown",
                    "compliance_requirement": "Corporate disclosure",
                    "processed_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error in AI analysis: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return None

    def _generate_fallback_headline(self, original_headline: str, doc_type: str) -> str:
        """Generate a fallback headline when AI fails"""
        if original_headline and len(original_headline) <= 80:
            return original_headline
        elif original_headline:
            return original_headline[:77] + "..."
        else:
            return f"Corporate {doc_type.replace('_', ' ').title()} Announcement"

    def get_all_record_count(self) -> int:
        """Get total count of all records"""
        if not self.supabase:
            return 0
        
        try:
            result = self.supabase.table('master_corporate_announcements').select('id', count='exact').execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Error getting record count: {e}")
            return 0

    def get_unprocessed_count(self) -> int:
        """Get count of records with NULL summary"""
        if not self.supabase:
            return 0
        
        try:
            result = self.supabase.table('master_corporate_announcements').select('id', count='exact').is_('summary', None).execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Error getting unprocessed count: {e}")
            return 0

    def process_all_records(self, batch_size: int = 50, skip_existing: bool = True, save_progress: bool = True, progress_file: str = 'processing_progress.json') -> Dict:
        """Process all records with progress tracking"""
        logger.info(f"Starting batch processing with batch_size={batch_size}, skip_existing={skip_existing}")
        
        stats = {
            'total_records': 0,
            'processed': 0,
            'failed': 0,
            'batches_completed': 0,
            'total_batches': 0,
            'success_rate': 0.0
        }
        
        try:
            # Get total count
            if skip_existing:
                total_count = self.get_unprocessed_count()
            else:
                total_count = self.get_all_record_count()
            
            stats['total_records'] = total_count
            stats['total_batches'] = (total_count + batch_size - 1) // batch_size
            
            logger.info(f"Processing {total_count} records in {stats['total_batches']} batches")
            
            # Process in batches
            for batch_num in range(stats['total_batches']):
                offset = batch_num * batch_size
                
                # Fetch batch
                query = self.supabase.table('master_corporate_announcements').select('*').range(offset, offset + batch_size - 1)
                if skip_existing:
                    query = query.is_('summary', None)
                
                batch_result = query.execute()
                batch_records = batch_result.data
                
                logger.info(f"Processing batch {batch_num + 1}/{stats['total_batches']} ({len(batch_records)} records)")
                
                # Process each record in batch
                for record in batch_records:
                    try:
                        record_id = record.get('id', 'unknown')
                        headline = record.get('headline', '') or ''
                        details = record.get('details', '') or ''

                        # Use headline as content if details is empty
                        content = details if details else headline

                        logger.debug(f"Processing record {record_id}: headline='{headline[:50] if headline else 'None'}...', details='{details[:50] if details else 'None'}...'")

                        # Classify document type
                        doc_type, classification = self.classify_document_type(
                            content,
                            headline,
                            details,
                            record_id
                        )

                        logger.debug(f"Classified as {doc_type} with confidence {classification.confidence_score}")

                        # Analyze with AI
                        analysis = self.analyze_document_with_ai(
                            content,
                            headline,
                            details,
                            doc_type
                        )
                        
                        if analysis:
                            logger.info(f"Generated analysis for {record_id}: headline='{analysis.get('headline', 'N/A')}'")

                            # Update record
                            update_result = self.supabase.table('master_corporate_announcements').update({
                                'summary': analysis
                            }).eq('id', record_id).execute()

                            if update_result.data:
                                stats['processed'] += 1
                                logger.debug(f"Successfully processed record {record_id}")
                            else:
                                stats['failed'] += 1
                                logger.warning(f"Failed to update record {record_id}")
                        else:
                            stats['failed'] += 1
                            logger.warning(f"AI analysis failed for record {record_id}")

                    except Exception as e:
                        stats['failed'] += 1
                        logger.error(f"Error processing record {record.get('id', 'unknown')}: {e}")
                        import traceback
                        logger.debug(f"Full traceback: {traceback.format_exc()}")
                
                stats['batches_completed'] += 1
                
                # Save progress
                if save_progress:
                    with open(progress_file, 'w') as f:
                        json.dump(stats, f, indent=2)
                
                logger.info(f"Batch {batch_num + 1} completed. Progress: {stats['processed']}/{total_count}")
            
            # Calculate final stats
            if stats['processed'] + stats['failed'] > 0:
                stats['success_rate'] = (stats['processed'] / (stats['processed'] + stats['failed'])) * 100
            
            logger.info(f"Processing completed. Processed: {stats['processed']}, Failed: {stats['failed']}, Success rate: {stats['success_rate']:.1f}%")
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
        
        return stats

    def get_comprehensive_stats(self) -> Dict:
        """Get comprehensive statistics about processed documents"""
        if not self.supabase:
            return {}
        
        try:
            # Get all records with summaries
            result = self.supabase.table('master_corporate_announcements').select('summary').not_('summary', 'is', None).execute()
            
            stats = {
                'total_processed': len(result.data),
                'by_type': {},
                'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0}
            }
            
            for record in result.data:
                summary = record.get('summary')
                if summary and isinstance(summary, dict):
                    # Count by type
                    doc_type = summary.get('type', 'unknown')
                    stats['by_type'][doc_type] = stats['by_type'].get(doc_type, 0) + 1
                    
                    # Count by confidence
                    confidence = summary.get('confidence_score', 0)
                    if confidence >= 0.8:
                        stats['confidence_distribution']['high'] += 1
                    elif confidence >= 0.6:
                        stats['confidence_distribution']['medium'] += 1
                    else:
                        stats['confidence_distribution']['low'] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting comprehensive stats: {e}")
            return {}

    def summarize_record_to_json(self, record_id: str, output_path: str = None) -> Dict:
        """
        Summarize a specific record and save the result to a JSON file locally.

        Args:
            record_id (str): The ID of the record to process
            output_path (str, optional): Path where to save the JSON file.
                                       If None, saves as 'summary_{record_id}.json'

        Returns:
            Dict: The analysis result or error information
        """
        if not self.supabase:
            error_msg = "Supabase client not available"
            logger.error(error_msg)
            return {"error": error_msg}

        try:
            # Fetch the specific record
            result = self.supabase.table('master_corporate_announcements').select('*').eq('id', record_id).execute()

            if not result.data:
                error_msg = f"Record with ID {record_id} not found"
                logger.error(error_msg)
                return {"error": error_msg}

            record = result.data[0]
            logger.info(f"Processing record {record_id} for JSON summary")

            # Extract record data
            headline = record.get('headline', '') or ''
            details = record.get('details', '') or ''
            content = details if details else headline

            # Classify document type
            doc_type, classification = self.classify_document_type(
                content,
                headline,
                details,
                record_id
            )

            logger.info(f"Classified record {record_id} as {doc_type} with confidence {classification.confidence_score}")

            # Analyze with AI
            analysis = self.analyze_document_with_ai(
                content,
                headline,
                details,
                doc_type
            )

            if not analysis:
                error_msg = f"AI analysis failed for record {record_id}"
                logger.error(error_msg)
                return {"error": error_msg}

            # Prepare the complete result with original record data
            complete_result = {
                "record_id": record_id,
                "original_data": {
                    "headline": headline,
                    "details": details,
                    "company_name": record.get('company_name', ''),
                    "broadcast_date_time": record.get('broadcast_date_time', ''),
                    "created_at": record.get('created_at', ''),
                    "updated_at": record.get('updated_at', ''),
                    "url": record.get('url', ''),
                    "exchange": record.get('exchange', ''),
                    "symbol": record.get('symbol', '')
                },
                "classification": {
                    "document_type": doc_type,
                    "confidence_score": classification.confidence_score,
                    "matched_keywords": classification.matched_keywords
                },
                "ai_analysis": analysis,
                "processing_metadata": {
                    "processed_at": datetime.now().isoformat(),
                    "analyzer_version": "enhanced_v1.0",
                    "model_used": self.config.get('model', 'unknown')
                }
            }

            # Determine output file path
            if output_path is None:
                output_path = f"summary_{record_id}.json"

            # Save to JSON file
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(complete_result, f, indent=2, ensure_ascii=False)

            logger.info(f"Successfully saved analysis for record {record_id} to {output_path}")

            # Also return the result
            return complete_result

        except Exception as e:
            error_msg = f"Error processing record {record_id}: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return {"error": error_msg}


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description="Enhanced AI Document Analyzer")
    parser.add_argument('--ids', type=str, help='Comma-separated list of record IDs to process')
    parser.add_argument('--batch-process', action='store_true', help='Process records in batches')
    parser.add_argument('--process-all', action='store_true', help='Process all records')
    parser.add_argument('--limit', type=int, default=50, help='Limit number of records to process')
    parser.add_argument('--skip-existing', action='store_true', help='Skip records that already have summaries')
    parser.add_argument('--force', action='store_true', help='Force reprocess existing summaries')
    parser.add_argument('--resume-from', type=str, help='Resume processing from specific record ID')
    parser.add_argument('--save-progress', action='store_true', help='Save progress to file')
    parser.add_argument('--progress-file', type=str, default='processing_progress.json', help='Progress file path')
    parser.add_argument('--summarize', type=str, help='Summarize a specific record ID and save to JSON')
    parser.add_argument('--output', type=str, help='Output path for JSON summary (used with --summarize)')
    
    args = parser.parse_args()

    # Initialize analyzer
    analyzer = DocumentAnalyzer(DEFAULT_CONFIG)

    if args.summarize:
        # Summarize specific record and save to JSON
        record_id = args.summarize.strip()
        output_path = args.output

        print(f"🔍 Summarizing record {record_id}...")
        result = analyzer.summarize_record_to_json(record_id, output_path)

        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return 1
        else:
            output_file = output_path or f"summary_{record_id}.json"
            print(f"✅ Successfully created summary: {output_file}")
            print(f"📄 Company: {result['original_data'].get('company_name', 'N/A')}")
            print(f"📅 Date: {result['original_data'].get('broadcast_date_time', 'N/A')}")
            print(f"📝 Type: {result['classification']['document_type']}")
            print(f"🎯 Confidence: {result['classification']['confidence_score']:.3f}")
            print(f"📰 Headline: {result['ai_analysis'].get('headline', 'N/A')}")
            return 0

    elif args.ids:
        # Process specific IDs
        ids = [id.strip() for id in args.ids.split(',')]
        logger.info(f"Processing {len(ids)} specific records")
        # Implementation for specific IDs would go here

    elif args.batch_process or args.process_all:
        # Batch processing
        skip_existing = args.skip_existing or not args.force
        stats = analyzer.process_all_records(
            batch_size=args.limit,
            skip_existing=skip_existing,
            save_progress=args.save_progress,
            progress_file=args.progress_file
        )
        
        print("\nProcessing completed:")
        print(f"Total processed: {stats['processed']}")
        print(f"Failed: {stats['failed']}")
        print(f"Success rate: {stats['success_rate']:.1f}%")
        
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
