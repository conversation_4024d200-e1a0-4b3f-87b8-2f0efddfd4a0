'use client';

import {
  Newspaper,
  Star,
  Briefcase,
  Calendar,
  Settings,
  HelpCircle,
  Target,
  X
} from 'lucide-react';

interface SidebarProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  onClose?: () => void;
}

const navigationItems = [
  { id: 'feed', label: 'Feed', icon: Newspaper },
  { id: 'watchlist', label: 'Watchlist', icon: Star },
  { id: 'portfolio', label: 'Portfolio', icon: Briefcase },
  { id: 'events', label: 'Events Calendar', icon: Calendar },
];

const bottomItems = [
  { id: 'settings', label: 'Settings', icon: Settings },
  { id: 'help', label: 'Help', icon: HelpCircle },
];

export default function Sidebar({ activeTab = 'feed', onTabChange, onClose }: SidebarProps) {
  const handleTabChange = (tab: string) => {
    onTabChange?.(tab);
    onClose?.(); // Close mobile sidebar when tab is selected
  };

  return (
    <aside className="w-64 lg:w-auto lg:col-span-2 border-r border-slate-200 p-6 flex flex-col justify-between bg-white h-full">
      <div>
        {/* Mobile Close Button */}
        <div className="flex items-center justify-between mb-6 lg:hidden">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-[#2c374b] rounded-lg flex items-center justify-center text-white shadow-md">
              <Target className="h-5 w-5" />
            </div>
            <h1 className="heading-sans-bold text-lg text-slate-800">StrikeDeck</h1>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Desktop Logo */}
        <div className="hidden lg:flex items-center gap-3 mb-10">
          <div className="w-10 h-10 bg-[#2c374b] rounded-lg flex items-center justify-center text-white shadow-md">
            <Target className="h-7 w-7" />
          </div>
          <h1 className="heading-sans-bold text-xl text-slate-800">StrikeDeck</h1>
        </div>

        {/* Menu Section */}
        <h2 className="body-sans-medium text-xs text-slate-400 uppercase tracking-wider mb-3">
          Menu
        </h2>
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleTabChange(item.id)}
                className={`nav-link body-sans w-full flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors ${
                  isActive ? 'active' : ''
                }`}
              >
                <Icon className="text-xl w-5 h-5" />
                <span>{item.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Bottom Section */}
      <div className="space-y-2">
        {bottomItems.map((item) => {
          const Icon = item.icon;
          
          return (
            <button
              key={item.id}
              onClick={() => handleTabChange(item.id)}
              className="nav-link body-sans w-full flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors"
            >
              <Icon className="text-xl w-5 h-5" />
              <span>{item.label}</span>
            </button>
          );
        })}
      </div>
    </aside>
  );
}
