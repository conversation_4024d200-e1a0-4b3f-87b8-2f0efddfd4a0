# Enhanced AI Document Analyzer

This is the enhanced version of the AI Document Analyzer with flexible document type detection and extensible architecture.

## Features

- **Flexible Document Type System**: Add/remove document types without code changes
- **Configuration-Based**: Document types defined in external YAML/JSON files
- **Enhanced Classification**: Fuzzy matching and weighted keyword scoring
- **Adaptive Confidence Scoring**: Context-aware confidence thresholds
- **Comprehensive Error Handling**: Robust fallback mechanisms and detailed logging
- **JSON Summary Export**: Summarize individual records and save detailed analysis to JSON files
- **Backward Compatibility**: All existing functionality preserved

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables** (required for OpenAI):
   ```bash
   set OPENAI_API_KEY=your_openai_api_key
   set SUPABASE_KEY=your_supabase_key
   ```

3. **Test with a Single Record**:
   ```bash
   python ai_document_analyzer.py --ids YOUR_RECORD_ID
   ```

4. **Run Example Demo**:
   ```bash
   python example_usage.py
   ```

## Usage Examples

### 🚀 Quick Start (Most Common)
```bash
# Process all unprocessed records (recommended)
python quick_process.py

# Preview what would be processed
python quick_process.py --preview

# Process with custom batch size
python quick_process.py --batch-size 25
```

### 📊 Comprehensive Batch Processing
```bash
# Check database status and estimates
python generate_all_summaries.py --preview

# Process only unprocessed records
python generate_all_summaries.py --process-unprocessed-only

# Process ALL records (including already processed)
python generate_all_summaries.py --process-all

# Resume interrupted processing
python generate_all_summaries.py --resume

# Check current progress
python generate_all_summaries.py --status
```

### 🔧 Individual Record Processing
```bash
# Process a single record
python ai_document_analyzer.py --ids 180ecf34-12c8-4d41-a6a6-89705a0551a0

# Process multiple records
python ai_document_analyzer.py --ids "id1,id2,id3"

# Batch process with limit
python ai_document_analyzer.py --batch-process --limit 50
```

### 📄 JSON Summary Export (NEW!)
```bash
# Summarize a specific record and save to JSON
python ai_document_analyzer.py --summarize YOUR_RECORD_ID

# Summarize with custom output path
python ai_document_analyzer.py --summarize YOUR_RECORD_ID --output my_analysis.json

# Using the dedicated summarize script
python summarize_record.py YOUR_RECORD_ID

# With verbose output
python summarize_record.py YOUR_RECORD_ID --verbose

# Test the summarize feature
python test_summarize_feature.py

# Process ALL records (new feature)
python ai_document_analyzer.py --process-all --limit 50

# Advanced options
python ai_document_analyzer.py --batch-process --skip-existing --resume-from RECORD_ID
```

## JSON Summary Output Format

When using the `--summarize` feature, the output JSON file contains:

```json
{
  "record_id": "your_record_id",
  "original_data": {
    "headline": "Original headline from database",
    "details": "Original details/content",
    "company_name": "Company Name",
    "broadcast_date_time": "2024-01-01T10:00:00",
    "exchange": "BSE/NSE",
    "symbol": "SYMBOL",
    "url": "source_url"
  },
  "classification": {
    "document_type": "earnings_call",
    "confidence_score": 0.85,
    "matched_keywords": ["earnings", "call", "results"]
  },
  "ai_analysis": {
    "headline": "AI-generated concise headline",
    "type": "earnings_call",
    "summary": "Comprehensive AI-generated summary",
    "key_disclosures": {
      "event": "Main event description",
      "key_points": "Important details and implications"
    },
    "confidence_score": 0.85,
    "filing_date": "2024-01-01",
    "regulatory_body": "BSE/NSE/SEBI"
  },
  "processing_metadata": {
    "processed_at": "2024-01-01T10:00:00",
    "analyzer_version": "enhanced_v1.0",
    "model_used": "gpt-4o-mini"
  }
}
```

### Configuration

The system automatically loads document types from:
- `document_types.yaml` (primary)
- `document_types.json` (alternative)
- `config/document_types.yaml`
- `config/document_types.json`

You can customize document types by editing `document_types.yaml`.

## Files

- `ai_document_analyzer.py` - Main enhanced analyzer with JSON export feature
- `summarize_record.py` - Dedicated script for summarizing individual records
- `test_summarize_feature.py` - Test script for the new summarize functionality
- `document_types.yaml` - Document type configuration
- `example_usage.py` - Feature demonstration
- `requirements.txt` - Python dependencies
- `DOCUMENT_TYPE_ENHANCEMENTS.md` - Detailed documentation

## Key Improvements

1. **Runtime Extensibility**: Add custom document types without restarting
2. **Better Classification**: Fuzzy matching catches edge cases
3. **Adaptive Thresholds**: Confidence requirements adjust to context
4. **Enhanced Logging**: Detailed classification tracking and error patterns
5. **Configuration Hot-Reload**: Update document types without downtime
6. **Comprehensive Statistics**: Detailed analytics on classification performance

## Environment Variables

- `OPENAI_API_KEY` - OpenAI API key (required)
- `SUPABASE_KEY` - Supabase service key
- `SUPABASE_URL` - Supabase project URL (optional, uses default)
- `LOG_LEVEL` - Logging level (DEBUG, INFO, WARNING, ERROR)

## Troubleshooting

1. **Import Errors**: Run `pip install -r requirements.txt`
2. **YAML Errors**: Check `document_types.yaml` syntax
3. **Database Errors**: Verify Supabase credentials
4. **API Errors**: Check OpenAI API key and limits

For detailed documentation, see `DOCUMENT_TYPE_ENHANCEMENTS.md`.
