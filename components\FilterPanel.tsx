'use client';

import { useState, useEffect } from 'react';
import { Filter, X, Calendar, Building, FileText } from 'lucide-react';
import { FilterState } from '@/lib/types';
import { fetchSubcategories, fetchCompanyNames } from '@/lib/supabase';

interface FilterPanelProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export default function FilterPanel({ filters, onFiltersChange, isOpen, onToggle }: FilterPanelProps) {
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [companies, setCompanies] = useState<string[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);

  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const [subcategoriesData, companiesData] = await Promise.all([
          fetchSubcategories(),
          fetchCompanyNames()
        ]);
        setSubcategories(subcategoriesData);
        setCompanies(companiesData.slice(0, 100)); // Limit to first 100 companies for performance
      } catch (error) {
        console.error('Error loading filter options:', error);
      } finally {
        setLoadingOptions(false);
      }
    };

    if (isOpen) {
      loadFilterOptions();
    }
  }, [isOpen]);

  const updateFilter = (key: keyof FilterState, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const updateDateRange = (key: 'start' | 'end', value: string) => {
    onFiltersChange({
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [key]: value,
      },
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      company: '',
      filingType: '',
      dateRange: { start: '', end: '' },
      subcategory: '',
    });
  };

  const hasActiveFilters = filters.search || filters.company || filters.subcategory || 
    filters.dateRange.start || filters.dateRange.end;

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
          hasActiveFilters 
            ? 'bg-[#2c374b] text-white border-[#2c374b]' 
            : 'bg-white text-slate-600 border-slate-300 hover:bg-slate-50'
        }`}
      >
        <Filter className="w-4 h-4" />
        <span className="body-sans-medium">Filters</span>
        {hasActiveFilters && (
          <span className="bg-white text-[#2c374b] text-xs px-2 py-0.5 rounded-full body-sans-medium">
            Active
          </span>
        )}
      </button>
    );
  }

  return (
    <div className="bg-white border border-slate-200 rounded-lg p-6 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-slate-600" />
          <h3 className="heading-sans text-slate-900">Filters</h3>
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="body-sans text-sm text-slate-500 hover:text-slate-700 transition-colors"
            >
              Clear All
            </button>
          )}
          <button
            onClick={onToggle}
            className="p-1 text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Filter Options */}
      <div className="space-y-6">
        {/* Company Filter */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-2">
            <Building className="w-4 h-4" />
            Company
          </label>
          <select
            value={filters.company}
            onChange={(e) => updateFilter('company', e.target.value)}
            className="w-full p-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-400 text-sm"
            disabled={loadingOptions}
          >
            <option value="">All Companies</option>
            {companies.map((company) => (
              <option key={company} value={company}>
                {company}
              </option>
            ))}
          </select>
        </div>

        {/* Filing Type Filter */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-2">
            <FileText className="w-4 h-4" />
            Filing Type
          </label>
          <select
            value={filters.subcategory}
            onChange={(e) => updateFilter('subcategory', e.target.value)}
            className="w-full p-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-400 text-sm"
            disabled={loadingOptions}
          >
            <option value="">All Types</option>
            {subcategories.map((subcategory) => (
              <option key={subcategory} value={subcategory}>
                {subcategory}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range Filter */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-slate-700 mb-2">
            <Calendar className="w-4 h-4" />
            Date Range
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-xs text-slate-500 mb-1 block">From</label>
              <input
                type="date"
                value={filters.dateRange.start}
                onChange={(e) => updateDateRange('start', e.target.value)}
                className="w-full p-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-400 text-sm"
              />
            </div>
            <div>
              <label className="text-xs text-slate-500 mb-1 block">To</label>
              <input
                type="date"
                value={filters.dateRange.end}
                onChange={(e) => updateDateRange('end', e.target.value)}
                className="w-full p-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-400 text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Apply Button */}
      <div className="mt-6 pt-4 border-t border-slate-200">
        <button
          onClick={onToggle}
          className="w-full px-4 py-2 bg-[#2c374b] text-white rounded-lg hover:bg-[#3b4a61] transition-colors"
        >
          Apply Filters
        </button>
      </div>
    </div>
  );
}
