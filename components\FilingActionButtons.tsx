'use client';

import React, { useState } from 'react';
import { ExternalLink, Sparkles, Building2, FileText, Link as LinkI<PERSON>, TrendingUp, TrendingDown, Target } from 'lucide-react';
import { CorporateFiling } from '@/lib/types';
import { aiAnalysisService, AIAnalysisResult } from '@/lib/aiAnalysisService';

interface FilingActionButtonsProps {
  filing: CorporateFiling;
}

interface ImportantLink {
  url: string;
  title: string;
  type: 'website' | 'document' | 'regulatory' | 'other';
}

export default function FilingActionButtons({ filing }: FilingActionButtonsProps) {
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<AIAnalysisResult | null>(null);
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);

  // Extract important links from the filing content
  const extractImportantLinks = (): ImportantLink[] => {
    const links: ImportantLink[] = [];
    
    // Add attachment file if available
    if (filing.attachmentfiles) {
      links.push({
        url: filing.attachmentfiles,
        title: 'Filing Document (PDF)',
        type: 'document'
      });
    }

    // Extract company website or related links from content
    const content = `${filing.headline || ''} ${filing.details || ''}`.toLowerCase();
    
    // Common patterns for important links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const matches = content.match(urlRegex);
    
    if (matches) {
      matches.forEach((url, index) => {
        if (index < 3) { // Limit to 3 additional links
          links.push({
            url: url,
            title: `Related Link ${index + 1}`,
            type: 'other'
          });
        }
      });
    }

    // Add regulatory filing links based on exchange
    if (filing.source_exchange === 'BSE' && filing.bse_code) {
      links.push({
        url: `https://www.bseindia.com/stock-share-price/${filing.company_name?.toLowerCase().replace(/\s+/g, '-')}/${filing.bse_code}/`,
        title: 'BSE Company Page',
        type: 'regulatory'
      });
    }
    
    if (filing.source_exchange === 'NSE' && filing.nse_symbol) {
      links.push({
        url: `https://www.nseindia.com/get-quotes/equity?symbol=${filing.nse_symbol}`,
        title: 'NSE Company Page',
        type: 'regulatory'
      });
    }

    return links;
  };

  const handleCheckoutCompany = () => {
    // Navigate to company-specific page or external link
    const companySymbol = filing.nse_symbol || filing.bse_code || filing.company_name;
    if (companySymbol) {
      // This could be customized to your company page structure
      window.open(`/company/${companySymbol}`, '_blank');
    }
  };

  const handleGetAIAnalysis = async () => {
    setIsGeneratingAI(true);
    try {
      const analysis = await aiAnalysisService.generateAnalysis(filing);
      setAiAnalysis(analysis);
      setShowAIAnalysis(true);
    } catch (error) {
      console.error('Error generating AI analysis:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const importantLinks = extractImportantLinks();

  const getLinkIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-3 h-3" />;
      case 'regulatory':
        return <Building2 className="w-3 h-3" />;
      default:
        return <LinkIcon className="w-3 h-3" />;
    }
  };

  const getLinkColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'text-blue-600 hover:text-blue-800';
      case 'regulatory':
        return 'text-green-600 hover:text-green-800';
      default:
        return 'text-slate-600 hover:text-slate-800';
    }
  };

  return (
    <div className="mt-4 space-y-3">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleCheckoutCompany}
          className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors body-sans-medium"
        >
          <Building2 className="w-4 h-4" />
          <span>Checkout {filing.nse_symbol || filing.bse_code || 'Company'}</span>
          <ExternalLink className="w-3 h-3" />
        </button>

        <button
          onClick={handleGetAIAnalysis}
          disabled={isGeneratingAI}
          className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed body-sans-medium"
        >
          <Sparkles className={`w-4 h-4 ${isGeneratingAI ? 'animate-spin' : ''}`} />
          <span>{isGeneratingAI ? 'Generating...' : 'Get AI Impact & Summary'}</span>
        </button>
      </div>

      {/* Important Links */}
      {importantLinks.length > 0 && (
        <div className="border-t border-slate-200 pt-3">
          <h4 className="text-xs font-medium text-slate-500 mb-2 body-sans-medium">Important Links:</h4>
          <div className="flex flex-wrap gap-2">
            {importantLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className={`inline-flex items-center gap-1 px-2 py-1 text-xs rounded border border-slate-200 hover:border-slate-300 transition-colors body-sans ${getLinkColor(link.type)}`}
              >
                {getLinkIcon(link.type)}
                <span>{link.title}</span>
                <ExternalLink className="w-2.5 h-2.5" />
              </a>
            ))}
          </div>
        </div>
      )}

      {/* AI Analysis Results */}
      {showAIAnalysis && aiAnalysis && (
        <div className="border-t border-slate-200 pt-3">
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Sparkles className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="heading-sans text-sm text-purple-900 mb-3">AI Impact Analysis</h4>

                {/* Impact and Sentiment Indicators */}
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div className="flex items-center gap-2">
                    <Target className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-600 text-sm body-sans">Impact:</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                      aiAnalysis.impact === 'high' ? 'bg-orange-100 text-orange-800' :
                      aiAnalysis.impact === 'medium' ? 'bg-blue-100 text-blue-800' :
                      'bg-slate-100 text-slate-800'
                    }`}>
                      {aiAnalysis.impact.charAt(0).toUpperCase() + aiAnalysis.impact.slice(1)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    {aiAnalysis.sentiment === 'positive' ? (
                      <TrendingUp className="w-4 h-4 text-green-500" />
                    ) : aiAnalysis.sentiment === 'negative' ? (
                      <TrendingDown className="w-4 h-4 text-red-500" />
                    ) : (
                      <Target className="w-4 h-4 text-slate-500" />
                    )}
                    <span className="text-slate-600 text-sm body-sans">Sentiment:</span>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                      aiAnalysis.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                      aiAnalysis.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                      'bg-slate-100 text-slate-800'
                    }`}>
                      {aiAnalysis.sentiment.charAt(0).toUpperCase() + aiAnalysis.sentiment.slice(1)}
                    </span>
                  </div>
                </div>

                {/* Key Insights */}
                {aiAnalysis.key_insights.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-xs font-medium text-slate-700 mb-1 body-sans-medium">Key Insights:</h5>
                    <ul className="space-y-1">
                      {aiAnalysis.key_insights.map((insight, index) => (
                        <li key={index} className="text-xs text-slate-600 body-sans flex items-start gap-1">
                          <span className="text-purple-400 mt-1">•</span>
                          <span>{insight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Market Implications */}
                <div className="mb-3">
                  <h5 className="text-xs font-medium text-slate-700 mb-1 body-sans-medium">Market Implications:</h5>
                  <p className="text-xs text-slate-600 body-sans">{aiAnalysis.market_implications}</p>
                </div>

                {/* Impact Reasoning */}
                <div className="mb-3">
                  <h5 className="text-xs font-medium text-slate-700 mb-1 body-sans-medium">Analysis Reasoning:</h5>
                  <p className="text-xs text-slate-600 body-sans">{aiAnalysis.impact_reasoning}</p>
                </div>

                {/* Confidence Score */}
                <div className="flex items-center gap-2 pt-2 border-t border-purple-100">
                  <span className="text-xs text-slate-500 body-sans">AI Confidence:</span>
                  <div className="flex items-center gap-1">
                    <div className="w-16 h-1.5 bg-purple-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-purple-500 rounded-full transition-all"
                        style={{ width: `${aiAnalysis.confidence_score * 100}%` }}
                      />
                    </div>
                    <span className="text-xs font-medium text-purple-700 body-sans-medium">
                      {Math.round(aiAnalysis.confidence_score * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
